#!/usr/bin/env python3
"""
Manual test script for PatientLite model to verify functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, '/home/<USER>/source/repos/arrow/python/pinnacle_io')

try:
    from pinnacle_io.models.patient_lite import PatientLite
    from pinnacle_io.models.institution import Institution
    from datetime import datetime
    
    print("✓ Successfully imported PatientLite and Institution models")
    
    # Test 1: Basic initialization
    print("\n--- Test 1: Basic initialization ---")
    patient = PatientLite(
        patient_id=123,
        patient_path="Institution_1/Mount_0/Patient_123",
        mount_point="Mount_0",
        last_name="<PERSON>",
        first_name="<PERSON>"
    )
    print(f"✓ Created patient: {patient}")
    print(f"  patient_id: {patient.patient_id}")
    print(f"  patient_path: {patient.patient_path}")
    print(f"  last_name: {patient.last_name}")
    print(f"  first_name: {patient.first_name}")
    
    # Test 2: Formatted description with datetime
    print("\n--- Test 2: Formatted description with datetime ---")
    patient2 = PatientLite(
        patient_id=456,
        patient_path="Institution_1/Mount_0/Patient_456",
        formatted_description="Doe&&Jane&&A&&MRN54321&&Dr. <PERSON>&&2025-01-15"
    )
    print(f"✓ Created patient with formatted description: {patient2}")
    print(f"  last_name: {patient2.last_name}")
    print(f"  first_name: {patient2.first_name}")
    print(f"  middle_name: {patient2.middle_name}")
    print(f"  medical_record_number: {patient2.medical_record_number}")
    print(f"  physician: {patient2.physician}")
    print(f"  last_modified: {patient2.last_modified}")
    print(f"  last_modified type: {type(patient2.last_modified)}")
    
    # Test 3: Partial formatted description
    print("\n--- Test 3: Partial formatted description ---")
    patient3 = PatientLite(
        patient_id=789,
        patient_path="Institution_1/Mount_0/Patient_789",
        formatted_description="Brown&&Alice"
    )
    print(f"✓ Created patient with partial description: {patient3}")
    print(f"  last_name: {patient3.last_name}")
    print(f"  first_name: {patient3.first_name}")
    print(f"  middle_name: {patient3.middle_name}")
    print(f"  medical_record_number: {patient3.medical_record_number}")
    print(f"  physician: {patient3.physician}")
    print(f"  last_modified: {patient3.last_modified}")
    
    # Test 4: Invalid datetime
    print("\n--- Test 4: Invalid datetime ---")
    patient4 = PatientLite(
        patient_id=999,
        patient_path="Institution_1/Mount_0/Patient_999",
        formatted_description="Test&&User&&M&&MRN999&&Dr. Test&&invalid-date"
    )
    print(f"✓ Created patient with invalid datetime: {patient4}")
    print(f"  last_modified: {patient4.last_modified}")
    
    # Test 5: Type hints verification
    print("\n--- Test 5: Type hints verification ---")
    print(f"✓ patient_id type annotation: {PatientLite.__annotations__.get('patient_id', 'Not found')}")
    print(f"✓ last_name type annotation: {PatientLite.__annotations__.get('last_name', 'Not found')}")
    print(f"✓ last_modified type annotation: {PatientLite.__annotations__.get('last_modified', 'Not found')}")
    print(f"✓ institution type annotation: {PatientLite.__annotations__.get('institution', 'Not found')}")
    
    print("\n🎉 All manual tests completed successfully!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure SQLAlchemy and other dependencies are installed")
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()
